#!/usr/bin/env python3
"""
Unified Live Trading Entry Point
Aligns all live trading components with proper transaction signing and Jupiter configuration.
"""

import asyncio
import logging
import json
import os
import sys
from datetime import datetime
from pathlib import Path
from dotenv import load_dotenv

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

# Load environment variables
load_dotenv()

# Configure logging with safe directory creation
def setup_logging():
    """Setup logging with safe directory creation."""
    try:
        # Ensure logs directory exists
        logs_dir = project_root / 'logs'
        logs_dir.mkdir(exist_ok=True)

        # Configure logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(logs_dir / 'unified_live_trading.log'),
                logging.StreamHandler()
            ]
        )
    except Exception as e:
        # Fallback to console-only logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[logging.StreamHandler()]
        )
        print(f"⚠️ Warning: Could not setup file logging: {e}")

setup_logging()
logger = logging.getLogger(__name__)

class UnifiedLiveTrader:
    """Unified live trading system with proper transaction signing and execution."""

    def __init__(self):
        """Initialize the unified live trader."""
        # Load environment variables with validation
        self.wallet_address = os.getenv('WALLET_ADDRESS')
        self.keypair_path = os.getenv('KEYPAIR_PATH')
        self.helius_api_key = os.getenv('HELIUS_API_KEY')
        self.birdeye_api_key = os.getenv('BIRDEYE_API_KEY')

        # Trading configuration
        self.dry_run = os.getenv('DRY_RUN', 'true').lower() == 'true'  # Default to dry run for safety
        self.paper_trading = os.getenv('PAPER_TRADING', 'false').lower() == 'true'
        self.trading_enabled = os.getenv('TRADING_ENABLED', 'false').lower() == 'true'

        # Components
        self.tx_prep_service = None
        self.executor = None
        self.tx_builder = None
        self.telegram_notifier = None

        # Validate critical environment variables
        self.validation_errors = []
        self._validate_environment()

    def _validate_environment(self):
        """Validate environment variables and configuration."""
        if not self.wallet_address:
            self.validation_errors.append("WALLET_ADDRESS not set")

        if not self.helius_api_key:
            self.validation_errors.append("HELIUS_API_KEY not set")

        if not self.dry_run and not self.keypair_path:
            self.validation_errors.append("KEYPAIR_PATH required for live trading")

        if self.keypair_path and not os.path.exists(self.keypair_path):
            self.validation_errors.append(f"Keypair file not found: {self.keypair_path}")

        # Log validation results
        if self.validation_errors:
            logger.warning("⚠️ Environment validation issues:")
            for error in self.validation_errors:
                logger.warning(f"   - {error}")
        else:
            logger.info("✅ Environment validation passed")

    async def initialize_components(self):
        """Initialize all trading components with proper configuration."""
        logger.info("🔧 Initializing trading components...")

        # Check for validation errors first
        if self.validation_errors:
            logger.error("❌ Cannot initialize components due to validation errors:")
            for error in self.validation_errors:
                logger.error(f"   - {error}")
            return False

        try:
            # Import required modules with error handling
            try:
                from phase_4_deployment.rpc_execution.tx_builder import TxBuilder
                from phase_4_deployment.rpc_execution.helius_client import HeliusClient
                from phase_4_deployment.rpc_execution.transaction_executor import TransactionExecutor
                logger.info("✅ Core modules imported successfully")
            except ImportError as e:
                logger.error(f"❌ Failed to import core modules: {e}")
                return False

            # Try to import transaction preparation service
            try:
                from solana_tx_utils.tx_prep import TransactionPreparationService

                # Initialize transaction preparation service
                rpc_url = f"https://mainnet.helius-rpc.com/?api-key={self.helius_api_key}"
                self.tx_prep_service = TransactionPreparationService(rpc_url)

                # Load keypair for signing
                if self.keypair_path and os.path.exists(self.keypair_path):
                    self.tx_prep_service.load_keypair('default', self.keypair_path)
                    logger.info(f"✅ Loaded keypair from {self.keypair_path}")
                else:
                    logger.error(f"❌ Keypair file not found: {self.keypair_path}")
                    return False

                logger.info("✅ Transaction preparation service initialized")

            except ImportError:
                logger.warning("⚠️ Transaction preparation service not available, using legacy builder")
                self.tx_prep_service = None

            # Load keypair if available
            keypair = None
            if self.keypair_path and os.path.exists(self.keypair_path):
                try:
                    # Try to import solders keypair
                    try:
                        from solders.keypair import Keypair
                        logger.info("✅ Solders keypair module available")
                    except ImportError:
                        logger.warning("⚠️ Solders not available, using fallback keypair handling")
                        keypair = None
                        # Continue without keypair for now
                        logger.info("✅ Continuing without keypair (dry run mode)")

                    if 'Keypair' in locals():
                        import json

                    # Try to load as JSON array first
                    try:
                        with open(self.keypair_path, 'r') as f:
                            keypair_data = json.load(f)
                        if isinstance(keypair_data, list) and len(keypair_data) in [32, 64]:
                            # Handle both 32-byte (private key only) and 64-byte (private + public key) formats
                            if len(keypair_data) == 64:
                                # Use the full 64-byte array
                                keypair_bytes = bytes(keypair_data)
                                keypair = Keypair.from_bytes(keypair_bytes)
                            else:
                                # 32-byte private key only, extend to 64 bytes
                                keypair_bytes = bytes(keypair_data + [0] * 32)
                                keypair = Keypair.from_bytes(keypair_bytes[:32])
                        else:
                            logger.error(f"❌ Invalid keypair format: expected 32 or 64-byte array, got {len(keypair_data) if isinstance(keypair_data, list) else 'non-array'}")
                            return False
                    except json.JSONDecodeError:
                        # Try to load as raw bytes
                        with open(self.keypair_path, 'rb') as f:
                            keypair_bytes = f.read()
                        keypair = Keypair.from_bytes(keypair_bytes)

                    logger.info(f"✅ Loaded keypair from {self.keypair_path}")
                except Exception as e:
                    logger.error(f"❌ Error loading keypair: {str(e)}")
                    return False

            # Initialize transaction builder
            self.tx_builder = TxBuilder(self.wallet_address, keypair=keypair)
            logger.info("✅ Transaction builder initialized")

            # Initialize Helius client
            helius_client = HeliusClient(api_key=self.helius_api_key)

            # Initialize transaction executor
            self.executor = TransactionExecutor(
                rpc_client=helius_client,
                keypair_path=self.keypair_path,
                max_retries=3,
                retry_delay=1.0
            )
            logger.info("✅ Transaction executor initialized")

            # Initialize Telegram notifier
            try:
                from core.notifications.telegram_notifier import TelegramNotifier
                self.telegram_notifier = TelegramNotifier()
                if self.telegram_notifier.enabled:
                    logger.info("✅ Telegram notifier initialized")
                else:
                    logger.warning("⚠️ Telegram notifier disabled (credentials not found)")
            except ImportError:
                logger.warning("⚠️ Telegram notifier not available")
                self.telegram_notifier = None

            return True

        except Exception as e:
            logger.error(f"❌ Error initializing components: {str(e)}")
            return False

    async def check_wallet_balance(self):
        """Check wallet balance and ensure sufficient funds."""
        logger.info("💰 Checking wallet balance...")

        try:
            from phase_4_deployment.rpc_execution.helius_client import HeliusClient

            client = HeliusClient(api_key=self.helius_api_key)
            balance_data = await client.get_balance(self.wallet_address)

            if isinstance(balance_data, dict) and 'balance_sol' in balance_data:
                balance_sol = balance_data['balance_sol']
                logger.info(f"✅ Wallet balance: {balance_sol:.6f} SOL")

                if balance_sol < 0.1:
                    logger.error(f"❌ Insufficient balance: {balance_sol:.6f} SOL (minimum 0.1 SOL required)")
                    return False

                return True
            else:
                logger.error(f"❌ Could not retrieve wallet balance: {balance_data}")
                return False

        except Exception as e:
            logger.error(f"❌ Error checking wallet balance: {str(e)}")
            return False

    async def build_jupiter_transaction(self, signal):
        """Build a Jupiter swap transaction from a trading signal."""
        logger.info(f"🔨 Building Jupiter transaction for {signal['market']}")

        try:
            if self.tx_prep_service:
                # Use advanced transaction preparation service
                logger.info("Using transaction preparation service for Jupiter swap")

                # Get recent blockhash
                blockhash = self.tx_prep_service.get_recent_blockhash()
                pubkey = self.tx_prep_service.get_active_pubkey()

                # For now, create a memo transaction as a test
                # In production, this would build actual Jupiter swap instructions
                instructions = [
                    {
                        "programId": "MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr",
                        "accounts": [
                            {
                                "pubkey": pubkey,
                                "isSigner": True,
                                "isWritable": True
                            }
                        ],
                        "data": f"Trade: {signal['action']} {signal['market']} {signal['size']}"
                    }
                ]

                # Build transaction
                tx_bytes = self.tx_prep_service.build_transaction(
                    instructions=instructions,
                    fee_payer=pubkey,
                    recent_blockhash=blockhash,
                    priority_fee_microlamports=10000,
                    compute_unit_limit=200000,
                    is_versioned=True
                )

                # Sign transaction
                signed_tx_bytes = self.tx_prep_service.sign_transaction(tx_bytes, is_versioned=True)

                logger.info("✅ Transaction built and signed with preparation service")
                return signed_tx_bytes

            else:
                # Use legacy transaction builder
                logger.info("Using legacy transaction builder for Jupiter swap")

                tx_message = await self.tx_builder.build_swap_tx(signal)
                if tx_message:
                    logger.info("✅ Transaction built with legacy builder")
                    return tx_message
                else:
                    logger.error("❌ Failed to build transaction with legacy builder")
                    return None

        except Exception as e:
            logger.error(f"❌ Error building Jupiter transaction: {str(e)}")
            return None

    async def execute_trade(self, signal):
        """Execute a trade from a trading signal."""
        logger.info(f"🚀 Executing trade: {signal['action']} {signal['market']} {signal['size']}")

        try:
            # Build transaction
            transaction = await self.build_jupiter_transaction(signal)
            if not transaction:
                logger.error("❌ Failed to build transaction")
                return None

            # Execute transaction
            if not self.dry_run:
                logger.info("💸 Executing live transaction...")
                start_time = datetime.now()

                result = await self.executor.execute_transaction(transaction)

                execution_time = (datetime.now() - start_time).total_seconds()

                if result and result.get('success', False):
                    logger.info(f"✅ Transaction executed successfully: {result.get('signature', 'N/A')}")
                    logger.info(f"⏱️ Execution time: {execution_time:.2f} seconds")

                    # Save trade record
                    await self.save_trade_record(signal, result, execution_time)

                    # Send Telegram notification
                    if self.telegram_notifier and self.telegram_notifier.enabled:
                        try:
                            trade_data = {
                                'signal': signal,
                                'position_data': {
                                    'position_size_sol': signal.get('size', 0),
                                    'position_size_usd': signal.get('size', 0) * signal.get('price', 0),
                                    'total_wallet_sol': 0  # Would need to fetch actual balance
                                },
                                'transaction_result': {
                                    'success': True,
                                    'signature': result.get('signature', 'N/A'),
                                    'execution_time': execution_time
                                },
                                'timestamp': datetime.now().isoformat()
                            }
                            await self.telegram_notifier.notify_trade_executed(trade_data)
                            logger.info("📱 Telegram notification sent")
                        except Exception as e:
                            logger.warning(f"⚠️ Failed to send Telegram notification: {e}")

                    return result
                else:
                    logger.error(f"❌ Transaction failed: {result.get('error', 'Unknown error')}")
                    return None
            else:
                logger.info("🧪 Dry run mode - transaction not executed")
                return {'success': True, 'signature': 'dry_run_test', 'provider': 'dry_run'}

        except Exception as e:
            logger.error(f"❌ Error executing trade: {str(e)}")
            return None

    async def save_trade_record(self, signal, result, execution_time):
        """Save trade record to file."""
        try:
            trade_record = {
                'timestamp': datetime.now().isoformat(),
                'signal': signal,
                'result': result,
                'execution_time': execution_time,
                'wallet_address': self.wallet_address,
                'mode': 'live' if not self.dry_run else 'dry_run'
            }

            # Save to trades directory
            trades_dir = 'output/live_production/trades'
            os.makedirs(trades_dir, exist_ok=True)

            filename = f"trade_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            filepath = os.path.join(trades_dir, filename)

            with open(filepath, 'w') as f:
                json.dump(trade_record, f, indent=2)

            logger.info(f"💾 Trade record saved: {filepath}")

        except Exception as e:
            logger.error(f"❌ Error saving trade record: {str(e)}")

    async def run_trading_cycle(self):
        """Run a single trading cycle."""
        logger.info("🔄 Running trading cycle...")

        try:
            # Import signal generation components with error handling
            try:
                from phase_4_deployment.data_router.birdeye_scanner import BirdeyeScanner
                from phase_4_deployment.signal_generator.signal_enricher import SignalEnricher
                logger.info("✅ Signal generation modules imported successfully")
            except ImportError as e:
                logger.warning(f"⚠️ Signal generation modules not available: {e}")
                # Use fallback signal generation
                return await self.run_fallback_trading_cycle()

            # Scan for opportunities
            scanner = BirdeyeScanner(self.birdeye_api_key)
            opportunities = await scanner.scan_for_opportunities()
            await scanner.close()

            logger.info(f"📊 Found {len(opportunities)} opportunities")

            # Generate signals from opportunities
            signals = []
            for opp in opportunities[:3]:  # Limit to top 3
                signal = {
                    'action': 'BUY',
                    'market': f"{opp.get('symbol', 'UNKNOWN')}-USDC",
                    'price': opp.get('price', 0),
                    'size': 0.01,  # Small test size
                    'confidence': opp.get('score', 0.5),
                    'timestamp': datetime.now().isoformat()
                }
                signals.append(signal)

            # Enrich signals
            if signals:
                enricher = SignalEnricher()
                enriched_signals = [enricher.enrich_signal(signal) for signal in signals]

                # Sort by priority score
                enriched_signals.sort(
                    key=lambda s: s.get('metadata', {}).get('priority_score', 0),
                    reverse=True
                )

                # Execute best signal
                best_signal = enriched_signals[0]
                logger.info(f"🎯 Selected best signal: {best_signal['market']} (score: {best_signal.get('metadata', {}).get('priority_score', 0)})")

                # Execute trade
                result = await self.execute_trade(best_signal)

                return {
                    'signals_generated': len(signals),
                    'signals_enriched': len(enriched_signals),
                    'trade_executed': result is not None,
                    'trade_result': result
                }
            else:
                logger.info("📭 No signals generated this cycle")
                return {
                    'signals_generated': 0,
                    'signals_enriched': 0,
                    'trade_executed': False,
                    'trade_result': None
                }

        except Exception as e:
            logger.error(f"❌ Error in trading cycle: {str(e)}")
            return {
                'error': str(e),
                'signals_generated': 0,
                'signals_enriched': 0,
                'trade_executed': False,
                'trade_result': None
            }

    async def run_fallback_trading_cycle(self):
        """Run a fallback trading cycle when advanced modules are not available."""
        logger.info("🔄 Running fallback trading cycle...")

        try:
            # Generate a simple test signal
            test_signal = {
                'action': 'BUY',
                'market': 'SOL-USDC',
                'price': 180.0,  # Approximate SOL price
                'size': 0.001,   # Very small test size
                'confidence': 0.7,
                'timestamp': datetime.now().isoformat(),
                'source': 'fallback_generator'
            }

            logger.info(f"🎯 Generated fallback signal: {test_signal['market']}")

            # Execute the test signal if trading is enabled
            if self.trading_enabled and not self.dry_run:
                result = await self.execute_trade(test_signal)
                return {
                    'signals_generated': 1,
                    'signals_enriched': 1,
                    'trade_executed': result is not None,
                    'trade_result': result,
                    'mode': 'fallback'
                }
            else:
                logger.info("🧪 Fallback mode - dry run or trading disabled")
                return {
                    'signals_generated': 1,
                    'signals_enriched': 1,
                    'trade_executed': False,
                    'trade_result': {'success': True, 'signature': 'fallback_dry_run'},
                    'mode': 'fallback_dry_run'
                }

        except Exception as e:
            logger.error(f"❌ Error in fallback trading cycle: {str(e)}")
            return {
                'error': str(e),
                'signals_generated': 0,
                'signals_enriched': 0,
                'trade_executed': False,
                'trade_result': None,
                'mode': 'fallback_error'
            }

    async def run_live_trading(self, duration_minutes=30):
        """Run live trading for specified duration."""
        logger.info(f"🚀 Starting live trading session for {duration_minutes} minutes")

        # Initialize components
        if not await self.initialize_components():
            logger.error("❌ Failed to initialize components")
            return False

        # Check wallet balance
        if not await self.check_wallet_balance():
            logger.error("❌ Wallet balance check failed")
            return False

        # Print configuration
        logger.info("📋 Trading Configuration:")
        logger.info(f"   Wallet: {self.wallet_address}")
        logger.info(f"   Dry Run: {self.dry_run}")
        logger.info(f"   Paper Trading: {self.paper_trading}")
        logger.info(f"   Trading Enabled: {self.trading_enabled}")

        # Send session start notification
        if self.telegram_notifier and self.telegram_notifier.enabled:
            try:
                await self.telegram_notifier.notify_session_started(duration_minutes / 60.0)
                logger.info("📱 Session start notification sent")
            except Exception as e:
                logger.warning(f"⚠️ Failed to send session start notification: {e}")

        # Run trading cycles
        start_time = datetime.now()
        end_time = start_time.timestamp() + (duration_minutes * 60)
        cycle_count = 0

        try:
            while datetime.now().timestamp() < end_time:
                cycle_count += 1
                logger.info(f"🔄 Starting cycle {cycle_count}")

                # Run trading cycle
                cycle_result = await self.run_trading_cycle()

                logger.info(f"📊 Cycle {cycle_count} results: {cycle_result}")

                # Wait before next cycle
                await asyncio.sleep(60)  # 1 minute between cycles

        except KeyboardInterrupt:
            logger.info("⏹️ Trading stopped by user")
        except Exception as e:
            logger.error(f"❌ Error in trading session: {str(e)}")
        finally:
            # Send session end notification
            if self.telegram_notifier and self.telegram_notifier.enabled:
                try:
                    session_duration = (datetime.now() - start_time).total_seconds() / 60
                    metrics = {
                        'cycles_completed': cycle_count,
                        'trades_executed': 0,  # Would need to track this
                        'trades_rejected': 0,  # Would need to track this
                        'session_duration_minutes': session_duration
                    }
                    await self.telegram_notifier.notify_session_ended(metrics)
                    logger.info("📱 Session end notification sent")
                except Exception as e:
                    logger.warning(f"⚠️ Failed to send session end notification: {e}")

            # Cleanup
            if self.executor:
                await self.executor.close()

            if self.telegram_notifier:
                await self.telegram_notifier.close()

            logger.info(f"🏁 Trading session completed. Ran {cycle_count} cycles.")

        return True


async def main():
    """Main function for unified live trading."""
    import argparse

    parser = argparse.ArgumentParser(description="Unified Live Trading System")
    parser.add_argument("--duration", type=float, default=30.0, help="Trading duration in minutes")
    parser.add_argument("--test-mode", action="store_true", help="Run in test mode with small trades")

    args = parser.parse_args()

    print("🚀 UNIFIED LIVE TRADING SYSTEM")
    print("="*60)
    print("⚠️  This system will execute REAL TRADES with REAL MONEY")
    print("="*60)

    # Create trader
    trader = UnifiedLiveTrader()

    # Run live trading
    success = await trader.run_live_trading(args.duration)

    if success:
        print("✅ Live trading session completed successfully")
        return 0
    else:
        print("❌ Live trading session failed")
        return 1


if __name__ == "__main__":
    exit(asyncio.run(main()))
