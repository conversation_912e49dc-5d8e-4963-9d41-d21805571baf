#!/usr/bin/env python3
"""
Live Trading Metrics Component

This module provides components for displaying live trading metrics in the Synergy7 Unified Dashboard.
"""

import pandas as pd
import streamlit as st
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import json

def render_live_trading_metrics(data: Dict[str, Any]):
    """
    Render live trading metrics components.

    Args:
        data: Dashboard data
    """
    st.header("🚀 Live Trading Metrics")

    # Get live trading data
    live_trading = data.get('live_trading', {})
    real_time_metrics = data.get('real_time_metrics', {})

    if not live_trading.get('trades') and live_trading.get('status') == 'inactive':
        st.info("🔄 Live trading system is currently inactive. Start live trading to see real-time metrics.")
        return

    # Create tabs for different live metrics
    tab1, tab2, tab3, tab4 = st.tabs([
        "🎯 Live Status",
        "📊 Real-Time P&L",
        "⚡ Live Transactions",
        "📈 Session Performance"
    ])

    with tab1:
        render_live_status(live_trading, real_time_metrics)

    with tab2:
        render_live_pnl(live_trading, real_time_metrics)

    with tab3:
        render_live_transactions(live_trading)

    with tab4:
        render_session_performance(live_trading, real_time_metrics)

def render_live_status(live_trading: Dict[str, Any], real_time_metrics: Dict[str, Any]):
    """
    Render live trading status.

    Args:
        live_trading: Live trading data
        real_time_metrics: Real-time metrics data
    """
    st.subheader("🎯 Live Trading Status")

    # Status indicators
    cols = st.columns(4)

    with cols[0]:
        status = live_trading.get('status', 'inactive')
        if status == 'active':
            st.metric("System Status", "🟢 ACTIVE", delta="Live")
        else:
            st.metric("System Status", "🔴 INACTIVE", delta="Stopped")

    with cols[1]:
        execution_stats = real_time_metrics.get('execution_stats', {})
        success_rate = execution_stats.get('success_rate', 0) * 100
        st.metric("Success Rate", f"{success_rate:.1f}%", delta=f"{execution_stats.get('successful_trades', 0)} successful")

    with cols[2]:
        total_trades = execution_stats.get('total_trades', 0)
        real_tx_count = execution_stats.get('real_transactions', 0)
        st.metric("Total Trades", f"{total_trades}", delta=f"{real_tx_count} real transactions")

    with cols[3]:
        system_health = real_time_metrics.get('system_health', {})
        session_duration = system_health.get('session_duration', 0)
        st.metric("Session Duration", f"{session_duration:.1f} min", delta="Active session")

    # Last trade info
    if live_trading.get('trades'):
        last_trade = live_trading['trades'][-1]
        st.markdown("### 🕐 Last Trade")

        trade_cols = st.columns(3)
        with trade_cols[0]:
            timestamp = last_trade.get('timestamp', '')
            try:
                dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                time_str = dt.strftime('%H:%M:%S')
            except:
                time_str = timestamp[:8] if len(timestamp) > 8 else timestamp
            st.write(f"**Time:** {time_str}")

        with trade_cols[1]:
            signal = last_trade.get('signal', {})
            st.write(f"**Action:** {signal.get('action', 'N/A')}")

        with trade_cols[2]:
            signal = last_trade.get('signal', {})
            st.write(f"**Size:** {signal.get('size', 0):.4f} SOL")

def render_live_pnl(live_trading: Dict[str, Any], real_time_metrics: Dict[str, Any]):
    """
    Render live P&L metrics.

    Args:
        live_trading: Live trading data
        real_time_metrics: Real-time metrics data
    """
    st.subheader("📊 Real-Time P&L")

    live_pnl = real_time_metrics.get('live_pnl', {})

    # P&L metrics
    pnl_cols = st.columns(2)

    with pnl_cols[0]:
        st.markdown("#### 💰 SOL Metrics")
        total_volume_sol = live_pnl.get('total_volume_sol', 0)
        st.metric("Total Volume (SOL)", f"{total_volume_sol:.4f}", delta="Live session")

        # Estimated P&L in SOL (placeholder - would need actual P&L calculation)
        estimated_pnl_sol = total_volume_sol * 0.001  # 0.1% estimated profit
        st.metric("Estimated P&L (SOL)", f"{estimated_pnl_sol:.6f}", delta="Estimated")

    with pnl_cols[1]:
        st.markdown("#### 💵 USD Metrics")
        total_volume_usd = live_pnl.get('total_volume_usd', 0)
        st.metric("Total Volume (USD)", f"${total_volume_usd:.2f}", delta="Live session")

        # Estimated P&L in USD
        estimated_pnl_usd = total_volume_usd * 0.001  # 0.1% estimated profit
        st.metric("Estimated P&L (USD)", f"${estimated_pnl_usd:.2f}", delta="Estimated")

    # Trade count metrics
    st.markdown("#### 📈 Trading Activity")
    activity_cols = st.columns(3)

    with activity_cols[0]:
        trade_count = live_pnl.get('trade_count', 0)
        st.metric("Total Trades", f"{trade_count}", delta="This session")

    with activity_cols[1]:
        real_tx_count = live_pnl.get('real_tx_count', 0)
        st.metric("Real Transactions", f"{real_tx_count}", delta="On blockchain")

    with activity_cols[2]:
        execution_rate = (real_tx_count / trade_count * 100) if trade_count > 0 else 0
        st.metric("Execution Rate", f"{execution_rate:.1f}%", delta="Real vs total")

    # P&L chart (placeholder)
    if live_trading.get('trades'):
        st.markdown("#### 📊 P&L Trend (Estimated)")

        # Create sample P&L data
        trades = live_trading['trades']
        timestamps = []
        cumulative_pnl = []
        running_pnl = 0

        for trade in trades:
            timestamp = trade.get('timestamp', '')
            signal = trade.get('signal', {})
            size_sol = signal.get('size', 0)
            price = signal.get('price', 180.0)
            position_size_usd = size_sol * price
            # Simulate P&L (in real system, this would be calculated from actual trades)
            trade_pnl = position_size_usd * 0.001  # 0.1% profit simulation
            running_pnl += trade_pnl

            timestamps.append(timestamp)
            cumulative_pnl.append(running_pnl)

        if timestamps and cumulative_pnl:
            pnl_df = pd.DataFrame({
                'Timestamp': timestamps,
                'Cumulative P&L (USD)': cumulative_pnl
            })

            fig = px.line(pnl_df, x='Timestamp', y='Cumulative P&L (USD)',
                         title='Live P&L Trend',
                         line_shape='linear')
            fig.update_layout(height=400)
            st.plotly_chart(fig, use_container_width=True)

def render_live_transactions(live_trading: Dict[str, Any]):
    """
    Display live transaction monitoring.

    Args:
        live_trading: Live trading data
    """
    st.subheader("⚡ Live Transactions")

    real_transactions = live_trading.get('real_transactions', [])

    if not real_transactions:
        st.info("🔄 No real transactions yet. Transactions will appear here when trades are executed on the blockchain.")
        return

    st.markdown(f"### 🔗 Real Blockchain Transactions ({len(real_transactions)})")

    # Create transaction table
    tx_data = []
    for tx in real_transactions[-10:]:  # Last 10 transactions
        tx_result = tx.get('result', {})
        signal = tx.get('signal', {})

        # Format timestamp
        timestamp = tx.get('timestamp', '')
        try:
            dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            time_str = dt.strftime('%H:%M:%S')
        except:
            time_str = timestamp[:8] if len(timestamp) > 8 else timestamp

        # Format signature for display
        signature = tx_result.get('signature', 'N/A')
        if signature and signature != 'N/A':
            sig_display = f"{signature[:8]}...{signature[-8:]}"
            sig_link = f"https://solscan.io/tx/{signature}"
        else:
            sig_display = "N/A"
            sig_link = ""

        # Calculate USD value from signal
        size_sol = signal.get('size', 0)
        price = signal.get('price', 180.0)
        size_usd = size_sol * price

        tx_data.append({
            'Time': time_str,
            'Action': signal.get('action', 'N/A'),
            'Size (SOL)': f"{size_sol:.4f}",
            'Size (USD)': f"${size_usd:.2f}",
            'Signature': sig_display,
            'Link': sig_link,
            'Status': '✅ Confirmed' if tx_result.get('success') else '❌ Failed',
            'Execution Time': f"{tx.get('execution_time', 0):.3f}s"
        })

    if tx_data:
        tx_df = pd.DataFrame(tx_data)

        # Display table
        st.dataframe(
            tx_df[['Time', 'Action', 'Size (SOL)', 'Size (USD)', 'Signature', 'Status', 'Execution Time']],
            use_container_width=True
        )

        # Add clickable links
        st.markdown("### 🔗 Transaction Links")
        for i, row in enumerate(tx_data):
            if row['Link']:
                st.markdown(f"**{row['Time']}** - [{row['Signature']}]({row['Link']}) - {row['Action']}")

def render_session_performance(live_trading: Dict[str, Any], real_time_metrics: Dict[str, Any]):
    """
    Render session performance metrics.

    Args:
        live_trading: Live trading data
        real_time_metrics: Real-time metrics data
    """
    st.subheader("📈 Session Performance")

    session_info = live_trading.get('session_info', {})
    metrics = live_trading.get('metrics', {})
    system_health = real_time_metrics.get('system_health', {})

    # Session overview
    st.markdown("#### 📊 Session Overview")
    session_cols = st.columns(4)

    with session_cols[0]:
        session_start = session_info.get('start_time', 'Unknown')
        st.write(f"**Start Time:** {session_start}")

    with session_cols[1]:
        cycles_completed = system_health.get('cycles_completed', 0)
        st.write(f"**Cycles Completed:** {cycles_completed}")

    with session_cols[2]:
        session_duration = system_health.get('session_duration', 0)
        st.write(f"**Duration:** {session_duration:.1f} min")

    with session_cols[3]:
        last_trade_time = system_health.get('last_trade', 'N/A')
        st.write(f"**Last Trade:** {last_trade_time}")

    # Performance metrics
    if metrics:
        st.markdown("#### 🎯 Performance Metrics")
        perf_cols = st.columns(3)

        with perf_cols[0]:
            trades_attempted = metrics.get('metrics', {}).get('trades_attempted', 0)
            st.metric("Trades Attempted", f"{trades_attempted}")

        with perf_cols[1]:
            trades_executed = metrics.get('metrics', {}).get('trades_executed', 0)
            st.metric("Trades Executed", f"{trades_executed}")

        with perf_cols[2]:
            execution_rate = (trades_executed / trades_attempted * 100) if trades_attempted > 0 else 0
            st.metric("Execution Rate", f"{execution_rate:.1f}%")

    # Trading activity chart
    if live_trading.get('trades'):
        st.markdown("#### 📊 Trading Activity Over Time")

        trades = live_trading['trades']

        # Group trades by time intervals
        trade_times = []
        for trade in trades:
            timestamp = trade.get('timestamp', '')
            try:
                dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                trade_times.append(dt)
            except:
                continue

        if trade_times:
            # Create hourly activity chart
            activity_df = pd.DataFrame({'timestamp': trade_times})
            activity_df['hour'] = activity_df['timestamp'].dt.floor('H')
            hourly_counts = activity_df.groupby('hour').size().reset_index(name='trade_count')

            fig = px.bar(hourly_counts, x='hour', y='trade_count',
                        title='Trading Activity by Hour',
                        labels={'hour': 'Hour', 'trade_count': 'Number of Trades'})
            fig.update_layout(height=400)
            st.plotly_chart(fig, use_container_width=True)
