#!/usr/bin/env python3
"""
Synergy7 Unified Dashboard

This module provides a unified Streamlit dashboard for the Synergy7 Trading System,
combining trading metrics, system monitoring, and advanced models in one interface.
"""

import os
import sys
import json
import time
import logging
import streamlit as st
import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Union

# Add the current directory to the Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.append(current_dir)

# Import data service
from data_service import data_service

# Import components
from components.trading_metrics import render_trading_metrics
from components.system_metrics import render_system_metrics
from components.market_data import render_market_data
from components.enhanced_pnl import render_enhanced_pnl
from components.enhanced_wallet_metrics import render_wallet_metrics, render_profit_metrics
from components.momentum_strategy import render_momentum_strategy
from components.live_trading_metrics import render_live_trading_metrics

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler(os.path.join(
            os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
            'output', 'unified_dashboard_log.txt'
        )),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("unified_dashboard")

# Import advanced models wrapper
try:
    from components.advanced_models_wrapper import render_advanced_models
    logger.info("Successfully imported advanced_models_wrapper")
except ImportError:
    logger.warning("Could not import advanced_models_wrapper, will use placeholder")

    def render_advanced_models(data):
        """Placeholder for advanced models component."""
        st.header("Advanced Models")
        st.info("Advanced models component could not be loaded. Please check the installation.")
        st.info("This component will be migrated from the existing gui_dashboard implementation.")



# Set page config
st.set_page_config(
    page_title="Synergy7 Trading System",
    page_icon="📊",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Global variables
REFRESH_INTERVAL = 5  # seconds

def render_header():
    """Render the dashboard header."""
    col1, col2, col3 = st.columns([1, 2, 1])

    with col1:
        st.image("https://solana.com/_next/static/media/logotype.e4df684f.svg", width=150)

    with col2:
        st.title("Synergy7 Trading System")
        st.caption("Unified dashboard for monitoring and analytics")

    with col3:
        st.write(f"Last updated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        if st.button("Refresh Data"):
            st.experimental_rerun()

def render_sidebar():
    """
    Render the dashboard sidebar.

    Returns:
        Selected page name
    """
    st.sidebar.title("Synergy7 Trading System")

    # Refresh interval
    refresh_interval = st.sidebar.slider(
        "Refresh Interval (seconds)",
        min_value=1,
        max_value=60,
        value=REFRESH_INTERVAL
    )

    # Mode selection
    mode = st.sidebar.selectbox(
        "Mode",
        ["Live", "Paper", "Simulation"],
        index=2
    )

    # Navigation options
    st.sidebar.title("Navigation")

    pages = {
        "Overview": "System status and key metrics",
        "🚀 Live Trading": "Real-time live trading metrics and monitoring",
        "Trading Metrics": "Detailed trading performance metrics",
        "Momentum Strategy": "Momentum strategy analytics and optimization",
        "Enhanced PnL": "Advanced profit and loss analytics",
        "Market Data": "Market information and opportunities",
        "Advanced Models": "Advanced trading models",
        "System Monitoring": "System health and performance",
        "Settings": "Configure dashboard settings"
    }

    selected_page = st.sidebar.radio("Select Page", list(pages.keys()))

    # Display page description
    st.sidebar.info(pages[selected_page])

    # Auto-refresh
    auto_refresh = st.sidebar.checkbox("Auto-refresh", value=False)

    # If auto-refresh is enabled, rerun the app after the specified interval
    if auto_refresh:
        refresh_interval = 30  # 30 seconds default
        time.sleep(refresh_interval)
        st.rerun()

    return selected_page

def render_overview(data: Dict[str, Any]):
    """
    Render the overview page.

    Args:
        data: Dashboard data
    """
    st.header("System Overview")

    # Key metrics
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        # Count active strategies
        active_strategies = 0
        if 'strategy_profiles' in data and not data['strategy_profiles'].empty:
            active_strategies = len(data['strategy_profiles'])
        st.metric("Active Strategies", active_strategies)

    with col2:
        # Count pending signals
        pending_signals = 0
        if 'enriched_signals' in data and 'signals' in data['enriched_signals']:
            pending_signals = len(data['enriched_signals']['signals'])
        st.metric("Pending Signals", pending_signals)

    with col3:
        # Count recent transactions
        recent_tx = 0
        if 'tx_history' in data and 'transactions' in data['tx_history']:
            recent_tx = sum(1 for tx in data['tx_history']['transactions']
                           if datetime.fromtimestamp(tx.get('timestamp', 0)) > datetime.now() - timedelta(hours=24))
        st.metric("24h Transactions", recent_tx)

    with col4:
        # Calculate success rate
        success_rate = 0
        if 'tx_history' in data and 'transactions' in data['tx_history'] and data['tx_history']['transactions']:
            success_count = sum(1 for tx in data['tx_history']['transactions']
                              if tx.get('status') in ['confirmed', 'finalized'])
            total_count = len(data['tx_history']['transactions'])
            success_rate = success_count / total_count * 100 if total_count > 0 else 0
        st.metric("Transaction Success Rate", f"{success_rate:.1f}%")

    # Wallet metrics
    render_wallet_metrics(data)

    # System health
    st.subheader("System Health")

    # Create columns for system health
    health_col1, health_col2, health_col3, health_col4 = st.columns(4)

    with health_col1:
        # Overall health (placeholder)
        overall_health = True
        st.metric(
            "Overall Health",
            "Healthy" if overall_health else "Unhealthy",
            delta=None,
            delta_color="normal"
        )

    with health_col2:
        # API health (placeholder)
        api_health = True
        st.metric(
            "API Health",
            "Healthy" if api_health else "Unhealthy",
            delta=None,
            delta_color="normal"
        )

    with health_col3:
        # Carbon Core health (placeholder)
        carbon_core_health = 'carbon_core' in data and bool(data['carbon_core'])
        st.metric(
            "Carbon Core",
            "Healthy" if carbon_core_health else "Unhealthy",
            delta=None,
            delta_color="normal"
        )

    with health_col4:
        # Wallet health (placeholder)
        wallet_health = True
        st.metric(
            "Wallet Health",
            "Healthy" if wallet_health else "Unhealthy",
            delta=None,
            delta_color="normal"
        )

    # Recent activity
    st.subheader("Recent Activity")

    if 'tx_history' in data and 'transactions' in data['tx_history'] and data['tx_history']['transactions']:
        # Convert to DataFrame for easier display
        tx_df = pd.DataFrame(data['tx_history']['transactions'])

        # Convert timestamp to datetime
        tx_df['datetime'] = pd.to_datetime(tx_df['timestamp'], unit='s')

        # Sort by timestamp (descending)
        tx_df = tx_df.sort_values('datetime', ascending=False)

        # Display recent transactions
        st.dataframe(tx_df[['datetime', 'signature', 'status']].head(10))
    else:
        st.info("No recent transactions found")

def render_settings():
    """Render the settings page."""
    st.header("Dashboard Settings")

    # Theme settings
    st.subheader("Theme Settings")
    theme = st.selectbox("Select Theme", ["Light", "Dark"])

    # Notification settings
    st.subheader("Notification Settings")
    st.checkbox("Enable Email Notifications", value=False)
    st.checkbox("Enable Slack Notifications", value=False)
    st.checkbox("Enable Telegram Notifications", value=True)

    # Auto-refresh settings
    st.subheader("Auto-Refresh Settings")
    refresh_interval = st.slider("Refresh Interval (seconds)", 0, 300, 60)

    # Save settings
    if st.button("Save Settings"):
        st.success("Settings saved successfully!")

def main():
    """Main function to run the dashboard."""
    # Load data
    data = data_service.load_all_data()

    # Render header
    render_header()

    # Render sidebar and get selected page
    selected_page = render_sidebar()

    # Render selected page
    if selected_page == "Overview":
        render_overview(data)
    elif selected_page == "🚀 Live Trading":
        render_live_trading_metrics(data)
    elif selected_page == "Trading Metrics":
        render_trading_metrics(data)
        render_profit_metrics(data)  # Add enhanced profit metrics
    elif selected_page == "Momentum Strategy":
        render_momentum_strategy(data)  # Render momentum strategy page
    elif selected_page == "Enhanced PnL":
        render_enhanced_pnl(data)
    elif selected_page == "Market Data":
        render_market_data(data)
    elif selected_page == "Advanced Models":
        render_advanced_models(data)
    elif selected_page == "System Monitoring":
        render_system_metrics(data)
        render_wallet_metrics(data)  # Add wallet metrics to system monitoring
    elif selected_page == "Settings":
        render_settings()

if __name__ == "__main__":
    main()
